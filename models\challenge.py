"""Data models for challenges and solutions."""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class DifficultyLevel(str, Enum):
    """Difficulty levels for challenges."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class ChallengeType(str, Enum):
    """Types of programming challenges."""
    ALGORITHMS = "algorithms"
    DATA_STRUCTURES = "data_structures"
    DYNAMIC_PROGRAMMING = "dynamic_programming"
    GRAPH_THEORY = "graph_theory"
    STRING_MANIPULATION = "string_manipulation"
    MATHEMATICAL_PROBLEMS = "mathematical_problems"

class Challenge(BaseModel):
    """Programming challenge data structure."""
    
    id: str = Field(description="Unique challenge identifier")
    title: str = Field(description="Challenge title")
    description: str = Field(description="Detailed problem description")
    difficulty: DifficultyLevel = Field(description="Difficulty level")
    challenge_type: ChallengeType = Field(description="Type of challenge")
    constraints: List[str] = Field(default=[], description="Problem constraints")
    examples: List[Dict[str, Any]] = Field(default=[], description="Input/output examples")
    hints: List[str] = Field(default=[], description="Optional hints")
    time_complexity_target: Optional[str] = Field(default=None, description="Target time complexity")
    space_complexity_target: Optional[str] = Field(default=None, description="Target space complexity")
    created_at: datetime = Field(default_factory=datetime.now)
    iteration: int = Field(default=1, description="Challenge iteration number")
    parent_challenge_id: Optional[str] = Field(default=None, description="ID of parent challenge if enhanced")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Solution(BaseModel):
    """Solution to a programming challenge."""
    
    id: str = Field(description="Unique solution identifier")
    challenge_id: str = Field(description="ID of the challenge this solves")
    code: str = Field(description="Solution code")
    language: str = Field(default="python", description="Programming language")
    approach: str = Field(description="High-level approach description")
    reasoning: str = Field(description="Detailed reasoning and methodology")
    step_by_step: List[str] = Field(description="Step-by-step solution process")
    time_complexity: Optional[str] = Field(default=None, description="Actual time complexity")
    space_complexity: Optional[str] = Field(default=None, description="Actual space complexity")
    test_cases: List[Dict[str, Any]] = Field(default=[], description="Test cases used")
    performance_notes: Optional[str] = Field(default=None, description="Performance observations")
    alternative_approaches: List[str] = Field(default=[], description="Other possible approaches")
    created_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class IterationRecord(BaseModel):
    """Record of a complete challenge-solution iteration."""
    
    iteration_number: int = Field(description="Iteration number in the sequence")
    challenge: Challenge = Field(description="The challenge for this iteration")
    solution: Optional[Solution] = Field(default=None, description="Solution if completed")
    enhancement_reasoning: Optional[str] = Field(default=None, description="Why/how challenge was enhanced")
    timestamp: datetime = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
