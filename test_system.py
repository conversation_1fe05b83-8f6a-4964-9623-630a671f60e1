"""Simple test script to verify system functionality."""

import asyncio
import os
import sys
from unittest.mock import Mock, patch

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.logger import logger
from config.settings import settings

def test_imports():
    """Test that all modules can be imported successfully."""
    try:
        # Test core imports
        from core.llm_client import LL<PERSON>lient
        from core.message_system import MessageBus, MessageFactory
        from core.logger import SystemLogger
        
        # Test model imports
        from models.challenge import Challenge, Solution, DifficultyLevel, ChallengeType
        from models.message import Message, MessageType, AgentType
        
        # Test agent imports
        from agents.challenge_creator import ChallengeCreatorAgent
        from agents.problem_solver import ProblemSolverAgent
        from agents.orchestrator import OrchestratorAgent
        
        # Test main system
        from main import MultiAgentSystem
        
        logger.console.print("[green]✅ All imports successful[/green]")
        return True
        
    except ImportError as e:
        logger.console.print(f"[red]❌ Import error: {e}[/red]")
        return False

def test_data_models():
    """Test that data models work correctly."""
    try:
        from models.challenge import Challenge, Solution, DifficultyLevel, ChallengeType
        from models.message import Message, MessageType, AgentType
        import uuid
        
        # Test Challenge model
        challenge = Challenge(
            id=str(uuid.uuid4()),
            title="Test Challenge",
            description="A test programming challenge",
            difficulty=DifficultyLevel.BEGINNER,
            challenge_type=ChallengeType.ALGORITHMS,
            constraints=["Time limit: 1 second"],
            examples=[{"input": "1, 2", "output": "3"}]
        )
        
        # Test Solution model
        solution = Solution(
            id=str(uuid.uuid4()),
            challenge_id=challenge.id,
            code="def solve(): return 42",
            approach="Simple approach",
            reasoning="Because 42 is the answer",
            step_by_step=["Step 1", "Step 2"]
        )
        
        # Test Message model
        message = Message(
            id=str(uuid.uuid4()),
            type=MessageType.CHALLENGE_REQUEST,
            sender=AgentType.ORCHESTRATOR,
            recipient=AgentType.CHALLENGE_CREATOR,
            content={"test": "data"}
        )
        
        logger.console.print("[green]✅ Data models working correctly[/green]")
        return True
        
    except Exception as e:
        logger.console.print(f"[red]❌ Data model error: {e}[/red]")
        return False

def test_configuration():
    """Test configuration system."""
    try:
        from config.settings import settings
        
        # Test that settings can be accessed
        model = settings.OPENAI_MODEL
        temp = settings.OPENAI_TEMPERATURE
        max_iter = settings.MAX_ITERATIONS
        
        # Test settings validation (should work even without API key for this test)
        try:
            settings.validate()
            logger.console.print("[green]✅ Configuration valid (API key found)[/green]")
        except ValueError:
            logger.console.print("[yellow]⚠️ Configuration test passed (API key not set - expected for testing)[/yellow]")
        
        return True
        
    except Exception as e:
        logger.console.print(f"[red]❌ Configuration error: {e}[/red]")
        return False

async def test_message_system():
    """Test the message system."""
    try:
        from core.message_system import MessageBus, MessageFactory
        from models.message import AgentType, MessageType
        
        # Create message bus
        bus = MessageBus()
        
        # Test message creation
        message = MessageFactory.create_challenge_request(
            AgentType.ORCHESTRATOR,
            AgentType.CHALLENGE_CREATOR,
            "algorithms",
            "beginner"
        )
        
        # Test message publishing (without starting the bus)
        await bus.publish(message)
        
        # Check message history
        history = bus.get_message_history()
        assert len(history) == 1
        assert history[0].type == MessageType.CHALLENGE_REQUEST
        
        logger.console.print("[green]✅ Message system working correctly[/green]")
        return True
        
    except Exception as e:
        logger.console.print(f"[red]❌ Message system error: {e}[/red]")
        return False

def test_mock_llm_integration():
    """Test LLM integration with mocked responses."""
    try:
        from core.llm_client import LLMClient
        
        # Create mock response
        mock_response = {
            "title": "Mock Challenge",
            "description": "A mock programming challenge for testing",
            "challenge_type": "algorithms",
            "constraints": ["Mock constraint"],
            "examples": [{"input": "test", "output": "result"}],
            "hints": ["Mock hint"]
        }
        
        # Test JSON parsing
        client = LLMClient()
        import json
        json_str = json.dumps(mock_response)
        parsed = client.parse_json_response(json_str)
        
        assert parsed["title"] == "Mock Challenge"
        
        logger.console.print("[green]✅ LLM client working correctly (mocked)[/green]")
        return True
        
    except Exception as e:
        logger.console.print(f"[red]❌ LLM client error: {e}[/red]")
        return False

async def run_all_tests():
    """Run all tests."""
    logger.console.print("[bold blue]🧪 Running Multi-Agent System Tests[/bold blue]\n")
    
    tests = [
        ("Import Test", test_imports),
        ("Data Models Test", test_data_models),
        ("Configuration Test", test_configuration),
        ("Message System Test", test_message_system),
        ("LLM Integration Test", test_mock_llm_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.console.print(f"Running {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.console.print(f"[red]❌ {test_name} failed with exception: {e}[/red]")
            results.append((test_name, False))
        
        logger.console.print()  # Add spacing
    
    # Summary
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    logger.console.print(f"[bold]Test Results: {passed}/{total} passed[/bold]")
    
    if passed == total:
        logger.console.print("[bold green]🎉 All tests passed! System is ready to use.[/bold green]")
        logger.console.print("\nTo run the system:")
        logger.console.print("1. Set your OPENAI_API_KEY in .env file")
        logger.console.print("2. Run: python main.py")
        logger.console.print("3. Or try: python examples/sample_run.py")
    else:
        logger.console.print("[bold red]❌ Some tests failed. Please check the errors above.[/bold red]")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_all_tests())
