"""Inter-agent communication system."""

import asyncio
import uuid
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
from collections import defaultdict, deque

from models.message import Message, MessageType, AgentType
from core.logger import logger

class MessageBus:
    """Message bus for inter-agent communication."""
    
    def __init__(self):
        self.subscribers: Dict[AgentType, List[Callable]] = defaultdict(list)
        self.message_queue: deque = deque()
        self.message_history: List[Message] = []
        self.running = False
    
    def subscribe(self, agent_type: AgentType, callback: Callable):
        """Subscribe an agent to receive messages."""
        self.subscribers[agent_type].append(callback)
        logger.logger.info(f"Agent {agent_type} subscribed to message bus")
    
    def unsubscribe(self, agent_type: AgentType, callback: Callable):
        """Unsubscribe an agent from receiving messages."""
        if callback in self.subscribers[agent_type]:
            self.subscribers[agent_type].remove(callback)
            logger.logger.info(f"Agent {agent_type} unsubscribed from message bus")
    
    async def publish(self, message: Message):
        """Publish a message to the bus."""
        # Log the message
        logger.log_message(message)
        
        # Add to history
        self.message_history.append(message)
        
        # Add to queue for processing
        self.message_queue.append(message)
        
        # Process immediately if running
        if self.running:
            await self._process_message(message)
    
    async def _process_message(self, message: Message):
        """Process a single message by delivering it to subscribers."""
        recipients = self.subscribers.get(message.recipient, [])
        
        if not recipients:
            logger.log_error(f"No subscribers for agent type: {message.recipient}")
            return
        
        # Deliver message to all subscribers of the recipient type
        for callback in recipients:
            try:
                await callback(message)
            except Exception as e:
                logger.log_error(f"Error delivering message to {message.recipient}: {str(e)}")
    
    async def start(self):
        """Start the message bus processing loop."""
        self.running = True
        logger.logger.info("Message bus started")
        
        while self.running:
            if self.message_queue:
                message = self.message_queue.popleft()
                await self._process_message(message)
            else:
                await asyncio.sleep(0.1)  # Small delay to prevent busy waiting
    
    def stop(self):
        """Stop the message bus."""
        self.running = False
        logger.logger.info("Message bus stopped")
    
    def get_message_history(self, agent_type: AgentType = None) -> List[Message]:
        """Get message history, optionally filtered by agent type."""
        if agent_type:
            return [msg for msg in self.message_history 
                   if msg.sender == agent_type or msg.recipient == agent_type]
        return self.message_history.copy()

class MessageFactory:
    """Factory for creating standardized messages."""
    
    @staticmethod
    def create_challenge_request(sender: AgentType, recipient: AgentType, 
                               challenge_type: str = None, difficulty: str = None) -> Message:
        """Create a challenge request message."""
        return Message(
            id=str(uuid.uuid4()),
            type=MessageType.CHALLENGE_REQUEST,
            sender=sender,
            recipient=recipient,
            content={
                "challenge_type": challenge_type,
                "difficulty": difficulty,
                "request_timestamp": datetime.now().isoformat()
            }
        )
    
    @staticmethod
    def create_challenge_response(sender: AgentType, recipient: AgentType, 
                                challenge_data: Dict[str, Any]) -> Message:
        """Create a challenge response message."""
        return Message(
            id=str(uuid.uuid4()),
            type=MessageType.CHALLENGE_RESPONSE,
            sender=sender,
            recipient=recipient,
            content={
                "challenge": challenge_data,
                "response_timestamp": datetime.now().isoformat()
            }
        )
    
    @staticmethod
    def create_solution_request(sender: AgentType, recipient: AgentType, 
                              challenge_id: str) -> Message:
        """Create a solution request message."""
        return Message(
            id=str(uuid.uuid4()),
            type=MessageType.SOLUTION_REQUEST,
            sender=sender,
            recipient=recipient,
            content={
                "challenge_id": challenge_id,
                "request_timestamp": datetime.now().isoformat()
            }
        )
    
    @staticmethod
    def create_solution_response(sender: AgentType, recipient: AgentType, 
                               solution_data: Dict[str, Any]) -> Message:
        """Create a solution response message."""
        return Message(
            id=str(uuid.uuid4()),
            type=MessageType.SOLUTION_RESPONSE,
            sender=sender,
            recipient=recipient,
            content={
                "solution": solution_data,
                "response_timestamp": datetime.now().isoformat()
            }
        )
    
    @staticmethod
    def create_enhancement_request(sender: AgentType, recipient: AgentType, 
                                 challenge_id: str, solution_data: Dict[str, Any]) -> Message:
        """Create an enhancement request message."""
        return Message(
            id=str(uuid.uuid4()),
            type=MessageType.ENHANCEMENT_REQUEST,
            sender=sender,
            recipient=recipient,
            content={
                "challenge_id": challenge_id,
                "solution": solution_data,
                "request_timestamp": datetime.now().isoformat()
            }
        )
    
    @staticmethod
    def create_error_message(sender: AgentType, recipient: AgentType, 
                           error: str, context: Dict[str, Any] = None) -> Message:
        """Create an error message."""
        return Message(
            id=str(uuid.uuid4()),
            type=MessageType.ERROR,
            sender=sender,
            recipient=recipient,
            content={
                "error": error,
                "context": context or {},
                "error_timestamp": datetime.now().isoformat()
            }
        )

# Global message bus instance
message_bus = MessageBus()
