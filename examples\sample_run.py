"""Sample run demonstrating the multi-agent system."""

import asyncio
import os
import sys

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import MultiAgentSystem
from core.logger import logger
from config.settings import settings

async def run_sample_session():
    """Run a sample session with different configurations."""
    
    logger.console.print("[bold cyan]🎯 Multi-Agent Challenge System - Sample Run[/bold cyan]")
    logger.console.print("This demonstrates the iterative challenge-solving loop.\n")
    
    system = MultiAgentSystem()
    
    try:
        # Initialize the system
        await system.initialize()
        
        # Run Session 1: Algorithms, starting from beginner
        logger.console.print("\n[bold yellow]📚 Session 1: Algorithm Challenges (Beginner → Advanced)[/bold yellow]")
        await system.start(
            challenge_type="algorithms",
            difficulty="beginner", 
            max_iterations=3
        )
        
        # Small delay between sessions
        await asyncio.sleep(2)
        
        # Create new system instance for second session
        system2 = MultiAgentSystem()
        await system2.initialize()
        
        # Run Session 2: Data Structures, starting from intermediate
        logger.console.print("\n[bold yellow]🏗️ Session 2: Data Structure Challenges (Intermediate → Expert)[/bold yellow]")
        await system2.start(
            challenge_type="data_structures",
            difficulty="intermediate",
            max_iterations=2
        )
        
    except Exception as e:
        logger.log_error(f"Sample run failed: {str(e)}")
        raise

async def run_custom_session():
    """Run a custom session with user-defined parameters."""
    
    logger.console.print("\n[bold cyan]🎮 Custom Session Configuration[/bold cyan]")
    
    # You can customize these parameters
    custom_config = {
        "challenge_type": "dynamic_programming",  # or None for random
        "difficulty": "intermediate",
        "max_iterations": 4
    }
    
    system = MultiAgentSystem()
    
    try:
        await system.initialize()
        
        logger.console.print(f"Starting custom session with:")
        logger.console.print(f"  Challenge Type: {custom_config['challenge_type']}")
        logger.console.print(f"  Initial Difficulty: {custom_config['difficulty']}")
        logger.console.print(f"  Max Iterations: {custom_config['max_iterations']}")
        
        await system.start(**custom_config)
        
    except Exception as e:
        logger.log_error(f"Custom session failed: {str(e)}")
        raise

async def demonstrate_system_features():
    """Demonstrate various system features."""
    
    logger.console.print("\n[bold cyan]🔧 System Features Demonstration[/bold cyan]")
    
    system = MultiAgentSystem()
    await system.initialize()
    
    # Show initial status
    status = system.get_status()
    logger.console.print(f"System Status: {status['system_running']}")
    
    # Start a short session to generate some data
    logger.console.print("\nRunning short demonstration session...")
    await system.start(
        challenge_type="string_manipulation",
        difficulty="beginner",
        max_iterations=2
    )
    
    # Show final status and statistics
    final_status = system.get_status()
    logger.console.print(f"\nFinal message count: {final_status.get('message_history_count', 0)}")

def main():
    """Main function for sample runs."""
    
    # Check if OpenAI API key is set
    if not settings.OPENAI_API_KEY:
        logger.console.print("[bold red]❌ Error: OPENAI_API_KEY environment variable not set[/bold red]")
        logger.console.print("Please set your OpenAI API key:")
        logger.console.print("  export OPENAI_API_KEY='your-api-key-here'")
        logger.console.print("  # or create a .env file with OPENAI_API_KEY=your-api-key-here")
        return
    
    logger.console.print("Choose a sample run:")
    logger.console.print("1. Standard sample session (2 different challenge types)")
    logger.console.print("2. Custom session (dynamic programming)")
    logger.console.print("3. System features demonstration")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(run_sample_session())
        elif choice == "2":
            asyncio.run(run_custom_session())
        elif choice == "3":
            asyncio.run(demonstrate_system_features())
        else:
            logger.console.print("[yellow]Invalid choice. Running standard sample session.[/yellow]")
            asyncio.run(run_sample_session())
            
    except KeyboardInterrupt:
        logger.console.print("\n[yellow]Sample run interrupted by user[/yellow]")
    except Exception as e:
        logger.log_error(f"Sample run error: {str(e)}")

if __name__ == "__main__":
    main()
