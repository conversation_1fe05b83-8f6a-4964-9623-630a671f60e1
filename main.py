"""Main entry point for the Multi-Agent Challenge System."""

import asyncio
import signal
import sys
from typing import Optional

from agents.challenge_creator import ChallengeCreatorAgent
from agents.problem_solver import ProblemSolverAgent
from agents.orchestrator import OrchestratorAgent
from core.message_system import message_bus
from core.logger import logger
from config.settings import settings

class MultiAgentSystem:
    """Main system coordinator for the multi-agent challenge system."""
    
    def __init__(self):
        self.challenge_creator: Optional[ChallengeCreatorAgent] = None
        self.problem_solver: Optional[ProblemSolverAgent] = None
        self.orchestrator: Optional[OrchestratorAgent] = None
        self.running = False
    
    async def initialize(self):
        """Initialize all agents and systems."""
        try:
            # Validate settings
            settings.validate()
            
            logger.console.print("[bold blue]🤖 Initializing Multi-Agent Challenge System[/bold blue]")
            
            # Initialize agents
            logger.console.print("Creating agents...")
            self.challenge_creator = ChallengeCreatorAgent()
            self.problem_solver = ProblemSolverAgent()
            self.orchestrator = OrchestratorAgent()
            
            logger.console.print("✅ All agents initialized")
            logger.logger.info("Multi-agent system initialized successfully")
            
        except Exception as e:
            logger.log_error(f"Failed to initialize system: {str(e)}")
            raise
    
    async def start(self, challenge_type: str = None, difficulty: str = "beginner", 
                   max_iterations: int = None):
        """Start the multi-agent system."""
        if self.running:
            logger.log_error("System is already running")
            return
        
        try:
            self.running = True
            
            # Override max iterations if specified
            if max_iterations:
                settings.MAX_ITERATIONS = max_iterations
                self.orchestrator.max_iterations = max_iterations
            
            # Start message bus
            logger.console.print("Starting message bus...")
            message_bus_task = asyncio.create_task(message_bus.start())
            
            # Small delay to ensure message bus is ready
            await asyncio.sleep(0.5)
            
            # Start orchestrator session
            logger.console.print("Starting orchestrator session...")
            await self.orchestrator.start_session(challenge_type, difficulty)
            
            # Keep system running until orchestrator finishes
            while self.orchestrator.running and self.running:
                await asyncio.sleep(1)
            
            # Stop message bus
            message_bus.stop()
            await message_bus_task
            
            logger.console.print("[bold green]System shutdown complete[/bold green]")
            
        except Exception as e:
            logger.log_error(f"System error: {str(e)}")
            raise
        finally:
            self.running = False
    
    def stop(self):
        """Stop the multi-agent system."""
        if not self.running:
            return
        
        logger.console.print("[yellow]Stopping system...[/yellow]")
        self.running = False
        
        if self.orchestrator:
            self.orchestrator.stop_session()
        
        message_bus.stop()
    
    def get_status(self):
        """Get system status."""
        if not self.orchestrator:
            return {"status": "not_initialized"}
        
        return {
            "system_running": self.running,
            "orchestrator_status": self.orchestrator.get_session_status(),
            "message_history_count": len(message_bus.get_message_history()),
            "settings": settings.to_dict()
        }

async def main():
    """Main function."""
    system = MultiAgentSystem()
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        logger.console.print("\n[yellow]Received interrupt signal. Shutting down...[/yellow]")
        system.stop()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize system
        await system.initialize()
        
        # Start with default parameters
        # You can modify these or add command line argument parsing
        await system.start(
            challenge_type="algorithms",  # or None for random
            difficulty="beginner",
            max_iterations=3
        )
        
    except KeyboardInterrupt:
        logger.console.print("\n[yellow]Interrupted by user[/yellow]")
    except Exception as e:
        logger.log_error(f"System failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
