"""Problem Solver Agent - Solves programming challenges and documents methodology."""

import uuid
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

from models.challenge import Challenge, Solution
from models.message import Message, MessageType, AgentType
from core.llm_client import llm_client
from core.message_system import message_bus, MessageFactory
from core.logger import logger

class ProblemSolverAgent:
    """Agent responsible for solving programming challenges and documenting the approach."""
    
    def __init__(self):
        self.agent_type = AgentType.PROBLEM_SOLVER
        self.current_solution: Optional[Solution] = None
        self.solution_history = []
        
        # Subscribe to message bus
        message_bus.subscribe(self.agent_type, self.handle_message)
    
    async def handle_message(self, message: Message):
        """Handle incoming messages."""
        try:
            if message.type == MessageType.SOLUTION_REQUEST:
                await self._handle_solution_request(message)
            elif message.type == MessageType.CHALLENGE_RESPONSE:
                await self._handle_challenge_response(message)
            else:
                logger.log_error(f"Unknown message type: {message.type}", str(self.agent_type))
        except Exception as e:
            logger.log_error(f"Error handling message: {str(e)}", str(self.agent_type))
            
            # Send error response
            error_msg = MessageFactory.create_error_message(
                self.agent_type, message.sender, str(e)
            )
            await message_bus.publish(error_msg)
    
    async def _handle_solution_request(self, message: Message):
        """Handle request to solve a challenge."""
        challenge_id = message.content.get("challenge_id")
        
        # This would typically involve retrieving the challenge from storage
        # For now, we'll assume the challenge is provided in the message
        if "challenge" in message.content:
            challenge_data = message.content["challenge"]
            challenge = Challenge(**challenge_data)
            solution = await self.solve_challenge(challenge)
            
            # Send solution response
            response = MessageFactory.create_solution_response(
                self.agent_type, message.sender, solution.dict()
            )
            await message_bus.publish(response)
    
    async def _handle_challenge_response(self, message: Message):
        """Handle receiving a challenge to solve."""
        challenge_data = message.content.get("challenge")
        if not challenge_data:
            raise ValueError("No challenge data in message")
        
        challenge = Challenge(**challenge_data)
        solution = await self.solve_challenge(challenge)
        
        # Send solution response back to sender
        response = MessageFactory.create_solution_response(
            self.agent_type, message.sender, solution.dict()
        )
        await message_bus.publish(response)
    
    async def solve_challenge(self, challenge: Challenge) -> Solution:
        """Solve a programming challenge with detailed documentation."""
        
        system_prompt = """You are an expert programmer and problem solver. Given a programming challenge, provide a complete solution with detailed analysis.

Your response must be valid JSON with the following structure:
{
    "code": "Complete working solution code",
    "language": "python",
    "approach": "High-level description of the solution approach",
    "reasoning": "Detailed explanation of why this approach works",
    "step_by_step": ["Step 1 description", "Step 2 description", ...],
    "time_complexity": "O(n) notation with explanation",
    "space_complexity": "O(n) notation with explanation", 
    "test_cases": [{"input": "test input", "output": "expected output", "description": "what this tests"}],
    "performance_notes": "Any performance considerations or optimizations",
    "alternative_approaches": ["Alternative approach 1", "Alternative approach 2", ...]
}

Focus on:
1. Correctness and efficiency
2. Clear, readable code with comments
3. Thorough analysis of complexity
4. Multiple test cases including edge cases
5. Alternative solution approaches"""
        
        user_prompt = f"""Solve this programming challenge:

Title: {challenge.title}
Description: {challenge.description}
Type: {challenge.challenge_type}
Difficulty: {challenge.difficulty}

Constraints:
{chr(10).join(f"- {constraint}" for constraint in challenge.constraints)}

Examples:
{chr(10).join(f"Input: {ex.get('input', 'N/A')} -> Output: {ex.get('output', 'N/A')}" for ex in challenge.examples)}

Target Complexities:
- Time: {challenge.time_complexity_target or 'Not specified'}
- Space: {challenge.space_complexity_target or 'Not specified'}

Provide a complete, working solution with thorough analysis."""
        
        messages = [{"role": "user", "content": user_prompt}]
        
        response = llm_client.generate_response_sync(messages, system_prompt, temperature=0.3)
        solution_data = llm_client.parse_json_response(response)
        
        # Create solution object
        solution = Solution(
            id=str(uuid.uuid4()),
            challenge_id=challenge.id,
            code=solution_data["code"],
            language=solution_data.get("language", "python"),
            approach=solution_data["approach"],
            reasoning=solution_data["reasoning"],
            step_by_step=solution_data["step_by_step"],
            time_complexity=solution_data.get("time_complexity"),
            space_complexity=solution_data.get("space_complexity"),
            test_cases=solution_data.get("test_cases", []),
            performance_notes=solution_data.get("performance_notes"),
            alternative_approaches=solution_data.get("alternative_approaches", [])
        )
        
        self.current_solution = solution
        self.solution_history.append(solution)
        logger.log_solution_created(solution)
        
        return solution
    
    def get_solution_by_challenge_id(self, challenge_id: str) -> Optional[Solution]:
        """Retrieve a solution by challenge ID."""
        for solution in self.solution_history:
            if solution.challenge_id == challenge_id:
                return solution
        return None
    
    def get_solution_statistics(self) -> Dict[str, Any]:
        """Get statistics about solved challenges."""
        if not self.solution_history:
            return {"total_solved": 0}
        
        languages = {}
        complexities = {"time": {}, "space": {}}
        approaches = {}
        
        for solution in self.solution_history:
            # Count languages
            lang = solution.language
            languages[lang] = languages.get(lang, 0) + 1
            
            # Count complexities
            if solution.time_complexity:
                complexities["time"][solution.time_complexity] = \
                    complexities["time"].get(solution.time_complexity, 0) + 1
            
            if solution.space_complexity:
                complexities["space"][solution.space_complexity] = \
                    complexities["space"].get(solution.space_complexity, 0) + 1
            
            # Count approach types (simplified)
            approach_key = solution.approach.split()[0].lower() if solution.approach else "unknown"
            approaches[approach_key] = approaches.get(approach_key, 0) + 1
        
        return {
            "total_solved": len(self.solution_history),
            "languages_used": languages,
            "time_complexities": complexities["time"],
            "space_complexities": complexities["space"],
            "common_approaches": approaches,
            "average_alternatives": sum(len(s.alternative_approaches) for s in self.solution_history) / len(self.solution_history)
        }
