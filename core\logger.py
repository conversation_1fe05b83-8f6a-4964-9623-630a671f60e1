"""Logging and tracking system for the multi-agent system."""

import logging
import json
import os
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.table import Table
from rich.panel import Panel

from config.settings import settings
from models.challenge import IterationRecord, Challenge, Solution
from models.message import Message

class SystemLogger:
    """Enhanced logging system for tracking agent interactions and iterations."""
    
    def __init__(self):
        self.console = Console()
        self.setup_logging()
        self.iterations: List[IterationRecord] = []
        self.messages: List[Message] = []
        
    def setup_logging(self):
        """Setup logging configuration."""
        # Create logs directory if it doesn't exist
        log_dir = Path(settings.LOG_FILE).parent
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, settings.LOG_LEVEL),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                <PERSON><PERSON><PERSON><PERSON>(console=self.console, rich_tracebacks=True),
                logging.FileHandler(settings.LOG_FILE)
            ]
        )
        
        self.logger = logging.getLogger("MultiAgentSystem")
    
    def log_message(self, message: Message):
        """Log an inter-agent message."""
        self.messages.append(message)
        self.logger.info(f"Message: {message.sender} -> {message.recipient} ({message.type})")
        
        # Display message in console
        self.console.print(Panel(
            f"[bold]{message.sender}[/bold] → [bold]{message.recipient}[/bold]\n"
            f"Type: {message.type}\n"
            f"Time: {message.timestamp.strftime('%H:%M:%S')}",
            title="Agent Communication"
        ))
    
    def log_iteration_start(self, iteration_number: int):
        """Log the start of a new iteration."""
        self.logger.info(f"Starting iteration {iteration_number}")
        self.console.print(f"\n[bold blue]🔄 Starting Iteration {iteration_number}[/bold blue]")
    
    def log_challenge_created(self, challenge: Challenge):
        """Log when a new challenge is created."""
        self.logger.info(f"Challenge created: {challenge.title} (Difficulty: {challenge.difficulty})")
        
        self.console.print(Panel(
            f"[bold green]Title:[/bold green] {challenge.title}\n"
            f"[bold green]Type:[/bold green] {challenge.challenge_type}\n"
            f"[bold green]Difficulty:[/bold green] {challenge.difficulty}\n"
            f"[bold green]Iteration:[/bold green] {challenge.iteration}",
            title="🎯 New Challenge Created"
        ))
    
    def log_solution_created(self, solution: Solution):
        """Log when a solution is created."""
        self.logger.info(f"Solution created for challenge {solution.challenge_id}")
        
        self.console.print(Panel(
            f"[bold blue]Approach:[/bold blue] {solution.approach}\n"
            f"[bold blue]Language:[/bold blue] {solution.language}\n"
            f"[bold blue]Time Complexity:[/bold blue] {solution.time_complexity or 'Not specified'}\n"
            f"[bold blue]Space Complexity:[/bold blue] {solution.space_complexity or 'Not specified'}",
            title="💡 Solution Created"
        ))
    
    def log_iteration_complete(self, record: IterationRecord):
        """Log completion of an iteration."""
        self.iterations.append(record)
        self.logger.info(f"Iteration {record.iteration_number} completed")
        
        self.console.print(f"[bold green]✅ Iteration {record.iteration_number} Complete[/bold green]\n")
    
    def log_error(self, error: str, agent: str = None):
        """Log an error."""
        self.logger.error(f"Error{f' in {agent}' if agent else ''}: {error}")
        self.console.print(f"[bold red]❌ Error{f' in {agent}' if agent else ''}: {error}[/bold red]")
    
    def display_summary(self):
        """Display a summary of all iterations."""
        if not self.iterations:
            self.console.print("[yellow]No iterations completed yet.[/yellow]")
            return
        
        table = Table(title="Iteration Summary")
        table.add_column("Iteration", style="cyan")
        table.add_column("Challenge", style="green")
        table.add_column("Difficulty", style="yellow")
        table.add_column("Solved", style="blue")
        table.add_column("Enhanced", style="magenta")
        
        for record in self.iterations:
            table.add_row(
                str(record.iteration_number),
                record.challenge.title[:30] + "..." if len(record.challenge.title) > 30 else record.challenge.title,
                record.challenge.difficulty,
                "✅" if record.solution else "❌",
                "✅" if record.enhancement_reasoning else "❌"
            )
        
        self.console.print(table)
    
    def save_session(self, filename: str = None):
        """Save the current session to a JSON file."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"logs/session_{timestamp}.json"
        
        session_data = {
            "timestamp": datetime.now().isoformat(),
            "iterations": [record.dict() for record in self.iterations],
            "messages": [message.dict() for message in self.messages],
            "settings": settings.to_dict()
        }
        
        Path(filename).parent.mkdir(exist_ok=True)
        with open(filename, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)
        
        self.logger.info(f"Session saved to {filename}")
        self.console.print(f"[green]Session saved to {filename}[/green]")

# Global logger instance
logger = SystemLogger()
