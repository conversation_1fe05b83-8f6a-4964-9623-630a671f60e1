"""Demo script showing the multi-agent system with mocked LLM responses."""

import asyncio
import json
import uuid
from unittest.mock import patch, <PERSON><PERSON>
from datetime import datetime

from main import MultiAgentSystem
from core.logger import logger
from models.challenge import Challenge, Solution, DifficultyLevel, ChallengeType

# Mock responses for demonstration
MOCK_CHALLENGE_RESPONSE = {
    "title": "Two Sum Problem",
    "description": "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice.",
    "challenge_type": "algorithms",
    "constraints": [
        "2 <= nums.length <= 10^4",
        "-10^9 <= nums[i] <= 10^9",
        "-10^9 <= target <= 10^9",
        "Only one valid answer exists"
    ],
    "examples": [
        {
            "input": "nums = [2,7,11,15], target = 9",
            "output": "[0,1]",
            "explanation": "Because nums[0] + nums[1] == 9, we return [0, 1]"
        },
        {
            "input": "nums = [3,2,4], target = 6", 
            "output": "[1,2]",
            "explanation": "Because nums[1] + nums[2] == 6, we return [1, 2]"
        }
    ],
    "hints": [
        "Try using a hash map to store values and their indices",
        "For each element, check if target - element exists in the hash map"
    ],
    "time_complexity_target": "O(n)",
    "space_complexity_target": "O(n)"
}

MOCK_SOLUTION_RESPONSE = {
    "code": """def twoSum(nums, target):
    # Create a hash map to store value -> index mapping
    num_map = {}
    
    for i, num in enumerate(nums):
        complement = target - num
        
        # Check if complement exists in our map
        if complement in num_map:
            return [num_map[complement], i]
        
        # Store current number and its index
        num_map[num] = i
    
    # Should never reach here given problem constraints
    return []""",
    "language": "python",
    "approach": "Hash table lookup for complement values",
    "reasoning": "We iterate through the array once, and for each element, we calculate its complement (target - current element). We use a hash map to store previously seen elements and their indices. If the complement exists in our hash map, we've found our pair.",
    "step_by_step": [
        "Initialize an empty hash map to store number -> index mappings",
        "Iterate through the array with both index and value",
        "For each element, calculate the complement (target - current element)",
        "Check if the complement exists in our hash map",
        "If found, return the indices [complement_index, current_index]",
        "If not found, store current element and index in hash map",
        "Continue until pair is found"
    ],
    "time_complexity": "O(n) - single pass through the array",
    "space_complexity": "O(n) - hash map can store up to n elements",
    "test_cases": [
        {
            "input": "nums=[2,7,11,15], target=9",
            "output": "[0,1]",
            "description": "Basic case with solution at beginning"
        },
        {
            "input": "nums=[3,2,4], target=6",
            "output": "[1,2]", 
            "description": "Solution not at beginning"
        },
        {
            "input": "nums=[3,3], target=6",
            "output": "[0,1]",
            "description": "Duplicate numbers case"
        }
    ],
    "performance_notes": "Hash map lookup is O(1) average case, making this very efficient. Space-time tradeoff compared to O(n²) brute force approach.",
    "alternative_approaches": [
        "Brute force: nested loops O(n²) time, O(1) space",
        "Two pointers: sort first O(n log n) time, but loses original indices",
        "Binary search: for each element, binary search for complement O(n log n) time"
    ]
}

MOCK_ENHANCED_CHALLENGE_RESPONSE = {
    "title": "Three Sum Problem",
    "description": "Given an integer array nums, return all the triplets [nums[i], nums[j], nums[k]] such that i != j, i != k, and j != k, and nums[i] + nums[j] + nums[k] == 0. Notice that the solution set must not contain duplicate triplets.",
    "challenge_type": "algorithms",
    "constraints": [
        "3 <= nums.length <= 3000",
        "-10^5 <= nums[i] <= 10^5",
        "Solution set must not contain duplicate triplets"
    ],
    "examples": [
        {
            "input": "nums = [-1,0,1,2,-1,-4]",
            "output": "[[-1,-1,2],[-1,0,1]]",
            "explanation": "The distinct triplets are [-1,0,1] and [-1,-1,2]"
        },
        {
            "input": "nums = [0,1,1]",
            "output": "[]",
            "explanation": "The only possible triplet does not sum up to 0"
        }
    ],
    "hints": [
        "Sort the array first to handle duplicates easily",
        "Use two pointers technique after fixing one element",
        "Skip duplicate elements to avoid duplicate triplets"
    ],
    "time_complexity_target": "O(n²)",
    "space_complexity_target": "O(1) excluding output array"
}

def mock_llm_response(*args, **kwargs):
    """Mock LLM response based on the prompt content."""
    messages = args[0] if args else kwargs.get('messages', [])
    
    # Check the content to determine what type of response to give
    if messages:
        content = messages[-1].get('content', '').lower()
        
        if 'enhance' in content or 'more difficult' in content:
            return json.dumps(MOCK_ENHANCED_CHALLENGE_RESPONSE)
        elif 'solve' in content or 'solution' in content:
            return json.dumps(MOCK_SOLUTION_RESPONSE)
        else:
            return json.dumps(MOCK_CHALLENGE_RESPONSE)
    
    return json.dumps(MOCK_CHALLENGE_RESPONSE)

async def run_demo():
    """Run a demonstration of the multi-agent system."""

    logger.console.print("[bold cyan]🎭 Multi-Agent Challenge System - DEMO MODE[/bold cyan]")
    logger.console.print("This demo uses mocked LLM responses to show system functionality.\n")

    # Patch both the LLM client and settings validation for demo
    with patch('core.llm_client.LLMClient.generate_response_sync', side_effect=mock_llm_response), \
         patch('config.settings.Settings.validate', return_value=True), \
         patch('config.settings.settings.OPENAI_API_KEY', 'demo-key'):

        system = MultiAgentSystem()

        try:
            # Initialize the system
            await system.initialize()
            
            logger.console.print("\n[bold yellow]🚀 Starting Demo Session[/bold yellow]")
            logger.console.print("Challenge Type: Algorithms")
            logger.console.print("Initial Difficulty: Beginner")
            logger.console.print("Max Iterations: 2")
            logger.console.print("LLM Responses: MOCKED for demonstration\n")
            
            # Start the system with demo parameters
            await system.start(
                challenge_type="algorithms",
                difficulty="beginner",
                max_iterations=2
            )
            
            logger.console.print("\n[bold green]🎉 Demo completed successfully![/bold green]")
            logger.console.print("\nThis demonstrated:")
            logger.console.print("✅ Agent initialization and communication")
            logger.console.print("✅ Challenge creation and enhancement")
            logger.console.print("✅ Solution generation and analysis")
            logger.console.print("✅ Iterative difficulty progression")
            logger.console.print("✅ Comprehensive logging and tracking")
            
        except Exception as e:
            logger.log_error(f"Demo failed: {str(e)}")
            raise

async def show_system_architecture():
    """Display system architecture information."""
    
    logger.console.print("\n[bold blue]🏗️ System Architecture Overview[/bold blue]")
    
    architecture_info = """
    ┌─────────────────────────────────────────────────────────────────┐
    │                    Multi-Agent Challenge System                 │
    └─────────────────────────────────────────────────────────────────┘
    
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │  Challenge      │    │  Problem        │    │  Orchestrator   │
    │  Creator        │◄──►│  Solver         │◄──►│  Agent          │
    │  Agent          │    │  Agent          │    │                 │
    │                 │    │                 │    │ • Manages loop  │
    │ • Creates       │    │ • Solves        │    │ • Coordinates   │
    │   challenges    │    │   problems      │    │   agents        │
    │ • Enhances      │    │ • Documents     │    │ • Tracks        │
    │   difficulty    │    │   methodology   │    │   progress      │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
             │                       │                       │
             └───────────────────────┼───────────────────────┘
                                     │
                        ┌─────────────────┐
                        │  Message Bus    │
                        │                 │
                        │ • Async comm    │
                        │ • Message queue │
                        │ • Event logging │
                        └─────────────────┘
                                     │
                        ┌─────────────────┐
                        │  Core Systems   │
                        │                 │
                        │ • LLM Client    │
                        │ • Data Models   │
                        │ • Configuration │
                        │ • Rich Logging  │
                        └─────────────────┘
    
    Key Features:
    • Asynchronous agent communication
    • Progressive difficulty scaling
    • Comprehensive solution analysis
    • Rich console output and logging
    • Configurable challenge types
    • Session tracking and export
    """
    
    logger.console.print(architecture_info)

def main():
    """Main demo function."""
    
    try:
        # Show architecture first
        asyncio.run(show_system_architecture())
        
        # Run the demo
        asyncio.run(run_demo())
        
        logger.console.print("\n[bold cyan]📚 Next Steps:[/bold cyan]")
        logger.console.print("1. Set up your OpenAI API key in .env file")
        logger.console.print("2. Run: python main.py (for real LLM integration)")
        logger.console.print("3. Try: python examples/sample_run.py")
        logger.console.print("4. Customize challenge types and difficulty levels")
        logger.console.print("5. Extend agents with additional capabilities")
        
    except KeyboardInterrupt:
        logger.console.print("\n[yellow]Demo interrupted by user[/yellow]")
    except Exception as e:
        logger.log_error(f"Demo error: {str(e)}")

if __name__ == "__main__":
    main()
