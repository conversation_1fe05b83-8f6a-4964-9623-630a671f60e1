"""Configuration settings for the multi-agent system."""

import os
from typing import Dict, Any
from dotenv import load_dotenv

load_dotenv()

class Settings:
    """Configuration settings for the multi-agent system."""
    
    # OpenAI API Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4")
    OPENAI_TEMPERATURE: float = float(os.getenv("OPENAI_TEMPERATURE", "0.7"))
    OPENAI_MAX_TOKENS: int = int(os.getenv("OPENAI_MAX_TOKENS", "2000"))
    
    # System Configuration
    MAX_ITERATIONS: int = int(os.getenv("MAX_ITERATIONS", "5"))
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/system.log")
    
    # Agent Configuration
    CHALLENGE_TYPES = [
        "algorithms",
        "data_structures", 
        "dynamic_programming",
        "graph_theory",
        "string_manipulation",
        "mathematical_problems"
    ]
    
    DIFFICULTY_LEVELS = [
        "beginner",
        "intermediate", 
        "advanced",
        "expert"
    ]
    
    # Communication timeouts (seconds)
    AGENT_TIMEOUT: int = int(os.getenv("AGENT_TIMEOUT", "30"))
    
    @classmethod
    def validate(cls) -> bool:
        """Validate that required settings are present."""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        return True
    
    @classmethod
    def to_dict(cls) -> Dict[str, Any]:
        """Convert settings to dictionary."""
        return {
            "openai_model": cls.OPENAI_MODEL,
            "openai_temperature": cls.OPENAI_TEMPERATURE,
            "max_iterations": cls.MAX_ITERATIONS,
            "challenge_types": cls.CHALLENGE_TYPES,
            "difficulty_levels": cls.DIFFICULTY_LEVELS
        }

# Global settings instance
settings = Settings()
