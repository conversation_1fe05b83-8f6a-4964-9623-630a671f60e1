# Multi-Agent Challenge System

A sophisticated multi-agent system where three LLM-powered agents collaborate in an iterative challenge-solving loop. The system demonstrates advanced AI agent coordination, progressive problem difficulty scaling, and comprehensive solution analysis.

## 🎯 System Overview

The system consists of three specialized agents:

1. **Challenge Creator Agent** - Generates and enhances programming challenges
2. **Problem Solver Agent** - Solves challenges with detailed methodology documentation  
3. **Orchestrator Agent** - Manages the iteration loop and agent coordination

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Challenge      │    │  Problem        │    │  Orchestrator   │
│  Creator        │◄──►│  Solver         │◄──►│  Agent          │
│  Agent          │    │  Agent          │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Message Bus    │
                    │  & Logger       │
                    └─────────────────┘
```

### Key Components

- **Message System**: Asynchronous inter-agent communication
- **LLM Integration**: OpenAI API wrapper with error handling
- **Logging System**: Rich console output and file logging
- **Data Models**: Pydantic models for challenges, solutions, and messages
- **Configuration**: Environment-based settings management

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- OpenAI API key

### Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd multi-agent-challenge-system
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

3. **Run the system**:
```bash
python main.py
```

### Example Run

```bash
# Run sample demonstrations
python examples/sample_run.py
```

## 📋 Features

### Challenge Creation
- **Dynamic Generation**: Creates programming challenges across multiple domains
- **Progressive Enhancement**: Automatically increases difficulty based on solution analysis
- **Multiple Types**: Algorithms, data structures, dynamic programming, graph theory, etc.
- **Comprehensive Details**: Constraints, examples, hints, complexity targets

### Problem Solving
- **Complete Solutions**: Working code with thorough analysis
- **Methodology Documentation**: Step-by-step reasoning and approach explanation
- **Complexity Analysis**: Time and space complexity evaluation
- **Alternative Approaches**: Multiple solution strategies
- **Test Case Generation**: Comprehensive test coverage

### System Orchestration
- **Iteration Management**: Handles complete challenge-solve-enhance cycles
- **Error Recovery**: Robust error handling and recovery mechanisms
- **Progress Tracking**: Real-time status monitoring and logging
- **Session Management**: Complete session lifecycle management

## 🔧 Configuration

### Environment Variables

```bash
# Required
OPENAI_API_KEY=your-api-key-here

# Optional (with defaults)
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=2000
MAX_ITERATIONS=5
LOG_LEVEL=INFO
LOG_FILE=logs/system.log
AGENT_TIMEOUT=30
```

### Challenge Types

- `algorithms` - General algorithmic problems
- `data_structures` - Array, tree, graph problems
- `dynamic_programming` - DP optimization problems
- `graph_theory` - Graph traversal and algorithms
- `string_manipulation` - String processing challenges
- `mathematical_problems` - Math-based programming problems

### Difficulty Levels

- `beginner` - Basic concepts and simple implementations
- `intermediate` - More complex logic and optimization
- `advanced` - Advanced algorithms and data structures
- `expert` - Complex, multi-concept problems

## 📊 Example Output

```
🤖 Initializing Multi-Agent Challenge System
✅ All agents initialized
Starting message bus...
Starting orchestrator session...

🚀 Starting Multi-Agent Challenge Session
Max iterations: 3
Initial challenge type: algorithms
Initial difficulty: beginner

🔄 Starting Iteration 1

┌─ Agent Communication ─┐
│ ORCHESTRATOR → CHALLENGE_CREATOR │
│ Type: challenge_request          │
│ Time: 14:30:15                  │
└─────────────────────────────────┘

🎯 New Challenge Created
Title: Two Sum Problem
Type: algorithms
Difficulty: beginner
Iteration: 1

💡 Solution Created
Approach: Hash table lookup
Language: python
Time Complexity: O(n)
Space Complexity: O(n)

📊 Iteration 1 Summary
Challenge: Two Sum Problem
Difficulty: beginner
Solution Approach: Hash table lookup
Time Complexity: O(n)
Space Complexity: O(n)
```

## 🧪 Testing

The system includes comprehensive logging and can be tested with:

```bash
# Run with different configurations
python main.py  # Default run

# Custom parameters in code
python examples/sample_run.py
```

## 📁 Project Structure

```
multi-agent-challenge-system/
├── agents/
│   ├── challenge_creator.py    # Challenge creation agent
│   ├── problem_solver.py       # Problem solving agent
│   └── orchestrator.py         # System orchestrator
├── core/
│   ├── llm_client.py          # OpenAI API wrapper
│   ├── message_system.py      # Inter-agent communication
│   └── logger.py              # Logging and tracking
├── models/
│   ├── challenge.py           # Challenge data models
│   └── message.py             # Message data structures
├── config/
│   └── settings.py            # Configuration management
├── examples/
│   └── sample_run.py          # Example usage
├── logs/                      # Generated log files
├── main.py                    # Main entry point
├── requirements.txt           # Dependencies
├── .env.example              # Environment template
└── README.md                 # This file
```

## 🔄 System Flow

1. **Initialization**: All agents subscribe to message bus
2. **Challenge Request**: Orchestrator requests initial challenge
3. **Challenge Creation**: Challenge Creator generates problem
4. **Solution Request**: Challenge sent to Problem Solver
5. **Solution Generation**: Problem Solver creates detailed solution
6. **Enhancement Request**: Solution analysis sent back to Challenge Creator
7. **Challenge Enhancement**: More difficult version created
8. **Iteration**: Process repeats until max iterations reached
9. **Session Complete**: Final summary and data export

## 🛠️ Customization

### Adding New Challenge Types

1. Update `ChallengeType` enum in `models/challenge.py`
2. Add type to `CHALLENGE_TYPES` in `config/settings.py`
3. Update Challenge Creator prompts to handle new type

### Extending Agent Capabilities

Each agent can be extended by:
- Adding new message handlers
- Implementing additional analysis methods
- Integrating external tools or APIs

### Custom Difficulty Progression

Modify the `enhance_challenge` method in `ChallengeCreatorAgent` to implement custom difficulty scaling logic.

## 📈 Performance Considerations

- **API Rate Limits**: Built-in retry logic and error handling
- **Memory Usage**: Efficient message queuing and history management
- **Scalability**: Async architecture supports concurrent operations
- **Logging**: Configurable log levels to manage output volume

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for the GPT API
- Rich library for beautiful console output
- Pydantic for data validation
- The Python async/await ecosystem
