"""Message data structures for inter-agent communication."""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class MessageType(str, Enum):
    """Types of messages that can be sent between agents."""
    CHALLENGE_REQUEST = "challenge_request"
    CHALLENGE_RESPONSE = "challenge_response"
    SOLUTION_REQUEST = "solution_request"
    SOLUTION_RESPONSE = "solution_response"
    ENHANCEMENT_REQUEST = "enhancement_request"
    SYSTEM_STATUS = "system_status"
    ERROR = "error"

class AgentType(str, Enum):
    """Types of agents in the system."""
    CHALLENGE_CREATOR = "challenge_creator"
    PROBLEM_SOLVER = "problem_solver"
    ORCHESTRATOR = "orchestrator"

class Message(BaseModel):
    """Base message structure for inter-agent communication."""
    
    id: str = Field(description="Unique message identifier")
    type: MessageType = Field(description="Type of message")
    sender: AgentType = Field(description="Agent that sent the message")
    recipient: AgentType = Field(description="Agent that should receive the message")
    timestamp: datetime = Field(default_factory=datetime.now)
    content: Dict[str, Any] = Field(description="Message content")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ChallengeMessage(Message):
    """Message containing a programming challenge."""
    
    def __init__(self, **data):
        super().__init__(**data)
        self.type = MessageType.CHALLENGE_RESPONSE

class SolutionMessage(Message):
    """Message containing a solution to a challenge."""
    
    def __init__(self, **data):
        super().__init__(**data)
        self.type = MessageType.SOLUTION_RESPONSE

class SystemMessage(Message):
    """System status or control message."""
    
    def __init__(self, **data):
        super().__init__(**data)
        self.type = MessageType.SYSTEM_STATUS

class ErrorMessage(Message):
    """Error message."""
    
    def __init__(self, **data):
        super().__init__(**data)
        self.type = MessageType.ERROR
